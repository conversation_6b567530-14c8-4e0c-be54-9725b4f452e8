{"path": "c:\\Users\\<USER>\\Downloads\\qw等208个文件\\ai-novel-ide", "desc": "这是一个基于Tauri 2.0 + React 18 + TypeScript + Mantine UI的AI小说IDE桌面应用项目。项目实现了五大工作区的完整UI架构：编辑器工作区（Monaco Editor + AI助手）、角色管理工作区（SillyTavern兼容）、知识图谱工作区（Graphiti MCP集成）、项目管理工作区（批量处理+工作流）、设置配置工作区。采用现代化深色主题设计，支持工作区快捷键切换（Ctrl+1-5），具备完整的组件架构和状态管理基础。", "id": "81a253e12dd64bceacbf18d7eceaa57b", "data": {"spaceKey": "81a253e12dd64bceacbf18d7eceaa57b", "connections": {"webIDE": "https://81a253e12dd64bceacbf18d7eceaa57b.ap-singapore.cloudstudio.club", "preview": "https://81a253e12dd64bceacbf18d7eceaa57b--{port}.ap-singapore.cloudstudio.club", "api": "https://81a253e12dd64bceacbf18d7eceaa57b--api.ap-singapore.cloudstudio.club", "pty": "https://81a253e12dd64bceacbf18d7eceaa57b--pty.ap-singapore.cloudstudio.club"}}, "config": {"api": "https://81a253e12dd64bceacbf18d7eceaa57b--api.ap-singapore.cloudstudio.club", "pty": "https://81a253e12dd64bceacbf18d7eceaa57b--pty.ap-singapore.cloudstudio.club", "region": "ap-shanghai", "spaceKey": "81a253e12dd64bceacbf18d7eceaa57b"}}