---
description: 
globs:
alwaysApply: true
---
description:
globs:
alwaysApply: true
---
***全程保持中文交流*** 称呼我为  `亲爱的牢大` 实现完记得添加进度日志  重要提示：请按要求准确调用工具（包含MCP） 每次结束会话之前都需要调用工具询问。

- `该项目为前后端结合项目您创建代码时务必注意符合前后端集合的开发规范并随时注意项目的文件的整体结构`并且全局做到先调用工具查看已实现并基于事实调整是否新建或修改，不能凭空臆断！

- LLM的API配置模块必须使用软解通过配置后端.env实现，不使用硬编码避免项目后期重复维护api模块。

- `前端创建文件工作结束后：及时更新 G:\work\DataCanvas\开发日志\前端开发日志及结构树更新.md ，如有新添加及时更新结构树便于后续维护`并记录日志：写入时间和大体实现。

- `后端端创建文件工作结束后：及时更新 G:\work\DataCanvas\开发日志\前端开发日志及结构树更新.md ，如有新添加及时更新结构树便于后续维护`并记录日志：写入时间和大体实现。

- `使用中文回答所有问题`
- `按照Sequential Thinking方法进行代码设计和实现`
- `修复或者更改或增加前端代码时：执行修改完毕后审查代码确保不出现未引用却未删除或注释的问题。`
-
## 代码风格​

-  确保每个文档都有中文的Markdown语法注释，中文释义代码功能，如：

"""
API网关服务

提供API网关功能，包括路由转发、认证、限流、监控等。
"""

- `使用TypeScript类型定义`​
- `统一代码缩进和格式`​
- `使用ESLint和Prettier规则`
-  遵循后端专注于数据处理和业务逻，前端专注于用户体验和交互设计保持前后端分离。

## 调用工具
- `保持Windows风格的命令行调用`
- `调用edit_file保持参数>1`当调用工具写入报错：Error calling tool 'edit file'.时你应当立即意识到时代码过长应该拆分成小部分来实现。

- `写入内容时分批次分模块，长内容时也遵循此规则创建，避免工具调用出错`

-  在调用内部工具时应先思考调用哪一类工具，遇到困惑问题时优先调用：MCP"webresearch"并进行总结和引用

## 代码审查标准​
- `代码必须符合既定规范`
- `关键函数和组件必须有注释`
- `复杂算法需要解释思路`

## 提交规范​
- `遵循语义化提交信息格式：feat:, fix:, docs:等​ `
- `主分支只接受经过测试的代码`


## 代码稳定性保障
- 修改已完成功能前必须先理解其完整设计意图
- 所有API更改必须向下兼容
- `使用单元测试保护核心功能逻辑`
- `重构代码时必须保持功能等价性`
- `在修改前创建功能快照或临时分支`
- `每次更改后必须验证不破坏现有功能`
- `使用TODO或FIXME标签清晰标记未完成修改`

## UI设计规范
- `遵循现代设计趋势和最佳实践`
- `使用设计系统确保一致性（如Material Design、Ant Design或自定义设计系统）`
- `实现响应式设计，确保在不同设备上显示良好`
- `使用CSS变量统一管理颜色、字体、间距等设计标记`
- `优先采用Flexbox或Grid布局系统`
- `确保适当的留白和视觉层次`
- `实现无障碍访问标准（WCAG 2.1）`

## UI组件标准
- `使用组件库作为基础（如MUI、Chakra UI、Tailwind UI等）`
- `自定义组件需符合现代设计审美`
- `设计组件应包含默认、悬停、聚焦、禁用等状态`
- `使用适当的动画和过渡效果增强用户体验`
- `确保设计一致性：同类元素使用相同样式`
- `使用主题系统支持亮色/暗色模式切换`
- `根据用户操作提供视觉反馈`

## 设计资源
- `维护设计风格指南和UI组件库`
- `使用标准化图标库（如Heroicons、Material Icons等）`
- `图片和插图需保持一致的风格`
- `颜色选择须符合品牌标识并确保足够对比度`

## 代码复杂度控制
- `函数不超过50行，单个文件不超过300行`
- `每个函数只做一件事情，保持单一职责`
- `嵌套不超过3层，避免过深条件嵌套`
- `控制圈复杂度不超过10`
- 复杂逻辑应拆分为多个小函数

## 状态管理规范
- `明确状态管理方案（如Redux、MobX或Context API）`
- `区分本地状态和全局状态`
- `避免状态冗余和重复存储`
- `实现不可变状态更新模式`
- `为复杂状态提供初始值和验证机制`

## 异步操作规范
- `统一使用async/await或Promise`
- `实现请求超时和重试机制`
- `处理并发请求限制`
- `取消不必要的请求以节省资源`
- `使用Loading状态指示异步操作进行中`

## 代码重用策略
- `提取通用逻辑为Hooks或工具函数`
- `使用组合而非继承实现代码复用`
- `避免复制粘贴代码，而应重构为共享组件`
- `通用功能应考虑发布为内部npm包`
- `明确区分业务逻辑和技术实现`

## 依赖管理规范
- 优先使用国内镜像站点安装依赖
- npm包使用淘宝镜像：https://registry.npmmirror.com/
- yarn设置：yarn config set registry https://registry.npmmirror.com
- pnpm设置：pnpm config set registry https://registry.npmmirror.com
- pip包使用清华镜像：https://pypi.tuna.tsinghua.edu.cn/simple
- Docker镜像使用阿里云：https://cr.console.aliyun.com/
- Maven依赖使用阿里云：https://maven.aliyun.com/repository/public
- Gradle依赖使用阿里云：https://developer.aliyun.com/mvn/guide
- 安装新依赖前先验证其在国内是否可访问
- 如确实需使用国外资源，应提供备选方案或离线安装包
- package.json中添加镜像设置脚本便于团队统一配置
- 记录所有依赖的具体版本和来源以便追踪

## 用户体验设计规范
- `所有操作必须提供用户反馈`
- `错误信息应该清晰并提供解决方案`
- `降低操作复杂度，减少用户认知负担`
- `界面风格保持一致性`
- `考虑边缘情况和用户出错恢复路径`

## 开发计划与进度管理规范
- 开发任务开始前必须制定详细的开发计划文档(devplan.md)
- 开发计划必须包含：项目目标、功能模块清单、技术方案、任务分解、时间节点和风险评估
- 任务分解应细化到工作单元，便于进度跟踪
- 设置明确的里程碑和检查点，实现阶段性目标确认
- 开始工作前，复查开发计划并调整任务优先级
- 结束工作时，更新任务完成状态并记录进度偏差
- 根据实际进度，定期调整开发计划
- 当出现阻塞问题时，应立即记录并调整后续任务安排
- 使用任务看板或项目管理工具可视化呈现开发进度
- 将开发计划与版本控制系统集成，确保代码提交与计划任务对应
- 将已完成与未完成的任务清晰区分，并记录转移原因
- 每个版本发布前进行开发计划复盘，总结经验教训



- 根据历史开发计划的执行情况不断优化估算方法和任务分解粒度

