{"title": "NovelCraft AI Studio - AI小说创作IDE", "features": ["模块化界面系统（5个专业工作区）", "全局酒馆控制系统（SillyTavern生态集成）", "智能知识库系统（RAG增强生成）", "知识图谱可视化系统（实体关系管理）", "多章节改编引擎（智能文本分析）", "AI工作流引擎（可视化流程设计）"], "tech": {"Desktop": "Tauri 2.0 + Rust后端 + SQLite数据库", "Frontend": "React 18 + TypeScript + Vite + Mantine UI + Zustand", "Editor": "Monaco Editor + 语法高亮 + 智能提示", "Visualization": "Cytoscape.js图谱可视化", "AI Integration": "多AI模型集成（OpenAI、Claude、DeepSeek）+ MCP协议", "Ecosystem": "SillyTavern生态 + 笔墨星河功能移植"}, "design": "专业IDE风格的深色主题界面，采用模块化工作区设计，包含编辑器、角色管理、知识图谱、项目管理、设置配置五大工作区。界面以深灰色为主调，蓝色作为强调色，注重功能性和专业性，支持多标签页和分屏显示，提供现代化的小说创作环境。", "plan": {"项目初始化和基础架构搭建": "done", "构建React前端基础框架和路由系统": "done", "开发模块化界面系统和工作区切换功能": "done", "实现Tauri后端核心服务和数据库设计": "doing", "实现Monaco编辑器集成和文档管理系统": "holding", "开发全局酒馆控制系统和AI模型集成": "holding", "构建智能知识库系统和RAG功能": "holding", "实现知识图谱可视化系统": "holding", "开发角色管理系统和SillyTavern集成": "holding", "构建多章节改编引擎和文本分析功能": "holding", "实现AI工作流引擎和可视化设计器": "holding", "集成MCP协议和动态指令工程系统": "holding", "开发批量处理引擎和任务调度系统": "holding", "实现应用打包、测试和部署流程": "holding"}}